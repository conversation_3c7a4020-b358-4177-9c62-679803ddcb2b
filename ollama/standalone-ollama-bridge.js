// standalone-ollama-bridge.js
const http = require('http');
const { spawn, exec } = require('child_process');
const { promisify } = require('util');
const crypto = require('crypto');
const url = require('url');

const execAsync = promisify(exec);

class TmuxOllamaManager {
    constructor() {
        this.sessions = new Map();
        this.busySessions = new Set();
        this.cleanupInterval = setInterval(() => this.cleanupOldSessions(), 10 * 60 * 1000);
    }

    async getOrCreateSession(modelName) {
        const sessionKey = `ollama_${modelName}`;
        
        if (!this.sessions.has(sessionKey)) {
            const sessionId = `${sessionKey}_${crypto.randomBytes(4).toString('hex')}`;
            await this.createSession(sessionId, modelName);
            
            this.sessions.set(sessionKey, {
                sessionId,
                modelName,
                lastUsed: Date.now(),
                created: Date.now()
            });
        }

        this.sessions.get(sessionKey).lastUsed = Date.now();
        return this.sessions.get(sessionKey).sessionId;
    }

    async createSession(sessionId, modelName) {
        try {
            await execAsync(`tmux kill-session -t ${sessionId} 2>/dev/null || true`);
            await execAsync(`tmux new-session -d -s ${sessionId}`);
            await execAsync(`tmux send-keys -t ${sessionId} "ollama run ${modelName}" Enter`);
            await this.waitForPrompt(sessionId, 30000);
            console.log(`Created tmux session: ${sessionId} for model: ${modelName}`);
        } catch (error) {
            console.error(`Failed to create session ${sessionId}:`, error);
            throw error;
        }
    }

    async waitForPrompt(sessionId, timeout = 30000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                const { stdout } = await execAsync(`tmux capture-pane -t ${sessionId} -p`);
                if (stdout.includes('>>>')) {
                    return true;
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`Error waiting for prompt in ${sessionId}:`, error);
            }
        }
        
        throw new Error(`Timeout waiting for prompt in session ${sessionId}`);
    }

    async sendPrompt(modelName, prompt) {
        const sessionId = await this.getOrCreateSession(modelName);
        
        if (this.busySessions.has(sessionId)) {
            throw new Error('Session is busy');
        }

        this.busySessions.add(sessionId);

        try {
            await execAsync(`tmux clear-history -t ${sessionId}`);
            
            const escapedPrompt = prompt
                .replace(/\\/g, '\\\\')
                .replace(/"/g, '\\"')
                .replace(/\n/g, '\\n');
            
            await execAsync(`tmux send-keys -t ${sessionId} "${escapedPrompt}" Enter`);
            
            const response = await this.collectResponse(sessionId);
            return response;
            
        } finally {
            this.busySessions.delete(sessionId);
        }
    }

    async collectResponse(sessionId, timeout = 300000) {
        return new Promise((resolve, reject) => {
            let lastContent = '';
            let checkInterval;
            let timeoutHandle;

            const cleanup = () => {
                if (checkInterval) clearInterval(checkInterval);
                if (timeoutHandle) clearTimeout(timeoutHandle);
            };

            timeoutHandle = setTimeout(() => {
                cleanup();
                reject(new Error('Response timeout'));
            }, timeout);

            checkInterval = setInterval(async () => {
                try {
                    const { stdout } = await execAsync(`tmux capture-pane -t ${sessionId} -p`);
                    
                    if (stdout !== lastContent) {
                        const lines = stdout.trim().split('\n');
                        const lastLine = lines[lines.length - 1];
                        
                        if (lastLine && lastLine.trim().startsWith('>>>')) {
                            const response = this.extractResponse(lines);
                            cleanup();
                            resolve(response);
                            return;
                        }
                        lastContent = stdout;
                    }
                } catch (error) {
                    cleanup();
                    reject(error);
                }
            }, 500);
        });
    }

    extractResponse(lines) {
        const responseLines = [];
        let foundStart = false;
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            
            if (trimmedLine.startsWith('>>>') || trimmedLine.startsWith('"')) {
                foundStart = true;
                continue;
            }
            
            if (foundStart && trimmedLine.startsWith('>>>')) {
                break;
            }
            
            if (foundStart && trimmedLine) {
                responseLines.push(line);
            }
        }
        
        return responseLines.join('\n').trim();
    }

    async cleanupOldSessions() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000;
        
        for (const [key, session] of this.sessions.entries()) {
            if (now - session.lastUsed > maxAge && !this.busySessions.has(session.sessionId)) {
                try {
                    await execAsync(`tmux kill-session -t ${session.sessionId}`);
                    this.sessions.delete(key);
                    console.log(`Cleaned up old session: ${session.sessionId}`);
                } catch (error) {
                    console.error(`Error cleaning up session ${session.sessionId}:`, error);
                }
            }
        }
    }

    async cleanup() {
        clearInterval(this.cleanupInterval);
        for (const session of this.sessions.values()) {
            try {
                await execAsync(`tmux kill-session -t ${session.sessionId}`);
            } catch (error) {
                console.error(`Error cleaning up session ${session.sessionId}:`, error);
            }
        }
    }
}

// HTTP 서버 (Express 없이)
const ollamaManager = new TmuxOllamaManager();

function parseBody(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(JSON.parse(body));
            } catch (error) {
                reject(error);
            }
        });
    });
}

const server = http.createServer(async (req, res) => {
    // CORS 헤더
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    const parsedUrl = url.parse(req.url, true);
    
    try {
        if (req.method === 'POST' && parsedUrl.pathname === '/api/generate') {
            const body = await parseBody(req);
            const { prompt, model = 'llama2' } = body;
            
            if (!prompt) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Prompt is required' }));
                return;
            }

            const response = await ollamaManager.sendPrompt(model, prompt);
            
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                response,
                model,
                done: true
            }));

        } else if (req.method === 'GET' && parsedUrl.pathname === '/api/models') {
            const { stdout } = await execAsync('ollama list');
            const lines = stdout.split('\n').slice(1);
            const models = lines
                .filter(line => line.trim())
                .map(line => {
                    const parts = line.split(/\s+/);
                    return {
                        name: parts[0],
                        size: parts[1] || 'unknown'
                    };
                });
            
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ models }));

        } else if (req.method === 'GET' && parsedUrl.pathname === '/health') {
            await execAsync('ollama list', { timeout: 5000 });
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ 
                status: 'healthy',
                timestamp: new Date().toISOString(),
                sessions: Array.from(ollamaManager.sessions.keys())
            }));

        } else {
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Not found' }));
        }

    } catch (error) {
        console.error('Request error:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: error.message }));
    }
});

const PORT = process.env.PORT || 8000;

server.listen(PORT, '0.0.0.0', () => {
    console.log(`Ollama Tmux Bridge API running on port ${PORT}`);
});

process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    await ollamaManager.cleanup();
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});