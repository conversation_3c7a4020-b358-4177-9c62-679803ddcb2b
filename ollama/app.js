// app.js - Ollama API Bridge with Node.js Terminal Control
import express from 'express';
import cors from 'cors';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import crypto from 'crypto';

const execAsync = promisify(exec);
const app = express();

// 미들웨어 설정
app.use(cors());
app.use(express.json());

class OllamaManager {
    constructor() {
        this.processes = new Map(); // 모델별 프로세스 관리
        this.busyProcesses = new Set();
        this.cleanupInterval = setInterval(() => this.cleanupOldProcesses(), 10 * 60 * 1000); // 10분마다
    }

    async getOrCreateProcess(modelName) {
        const processKey = `ollama_${modelName}`;
        
        if (!this.processes.has(processKey) || this.processes.get(processKey).killed) {
            const processId = `${processKey}_${crypto.randomBytes(4).toString('hex')}`;
            const process = await this.createProcess(processId, modelName);
            
            this.processes.set(processKey, {
                process,
                processId,
                modelName,
                lastUsed: Date.now(),
                created: Date.now()
            });
        }

        this.processes.get(processKey).lastUsed = Date.now();
        return this.processes.get(processKey);
    }

    async createProcess(processId, modelName) {
        return new Promise((resolve, reject) => {
            console.log(`Creating process for model: ${modelName}`);
            
            const process = spawn('ollama', ['run', modelName], {
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: false
            });

            let isReady = false;
            let outputBuffer = '';

            const timeout = setTimeout(() => {
                if (!isReady) {
                    process.kill();
                    reject(new Error(`Timeout waiting for model ${modelName} to load`));
                }
            }, 60000); // 60초 타임아웃

            process.stdout.on('data', (data) => {
                const output = data.toString();
                outputBuffer += output;
                
                // 모델이 준비되었는지 확인 (>>> 프롬프트 대기)
                if (output.includes('>>>') && !isReady) {
                    isReady = true;
                    clearTimeout(timeout);
                    console.log(`Model ${modelName} is ready`);
                    resolve(process);
                }
            });

            process.stderr.on('data', (data) => {
                console.error(`Process stderr: ${data}`);
            });

            process.on('error', (error) => {
                clearTimeout(timeout);
                reject(error);
            });

            process.on('exit', (code) => {
                console.log(`Process exited with code: ${code}`);
                clearTimeout(timeout);
                if (!isReady) {
                    reject(new Error(`Process exited before ready: ${code}`));
                }
            });
        });
    }

    async sendPrompt(modelName, prompt) {
        const processInfo = await this.getOrCreateProcess(modelName);
        const { process, processId } = processInfo;
        
        if (this.busyProcesses.has(processId)) {
            throw new Error('Process is busy');
        }

        this.busyProcesses.add(processId);

        try {
            return await this.executePrompt(process, prompt);
        } finally {
            this.busyProcesses.delete(processId);
        }
    }

    async executePrompt(process, prompt) {
        return new Promise((resolve, reject) => {
            let responseBuffer = '';
            let isCollecting = false;
            
            const timeout = setTimeout(() => {
                reject(new Error('Response timeout'));
            }, 300000); // 5분 타임아웃

            const dataHandler = (data) => {
                const output = data.toString();
                responseBuffer += output;
                
                // 응답 수집 시작 감지
                if (!isCollecting && output.includes('\n')) {
                    isCollecting = true;
                }
                
                // 응답 완료 감지 (>>> 프롬프트 재등장)
                if (isCollecting && output.includes('>>>')) {
                    clearTimeout(timeout);
                    process.stdout.removeListener('data', dataHandler);
                    
                    // 응답 추출 및 정리
                    const response = this.extractResponse(responseBuffer);
                    resolve(response);
                }
            };

            process.stdout.on('data', dataHandler);
            
            // 프롬프트 전송
            process.stdin.write(prompt + '\n');
        });
    }

    extractResponse(buffer) {
        const lines = buffer.split('\n');
        const responseLines = [];
        let foundStart = false;
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            
            // 입력 프롬프트 스킵
            if (trimmedLine.startsWith('>>>')) {
                if (foundStart) {
                    break; // 응답 끝
                }
                foundStart = true;
                continue;
            }
            
            // 응답 라인 수집
            if (foundStart && trimmedLine) {
                responseLines.push(line);
            }
        }
        
        return responseLines.join('\n').trim();
    }

    async sendStreamingPrompt(modelName, prompt, responseStream) {
        const processInfo = await this.getOrCreateProcess(modelName);
        const { process, processId } = processInfo;
        
        if (this.busyProcesses.has(processId)) {
            throw new Error('Process is busy');
        }

        this.busyProcesses.add(processId);

        try {
            await this.executeStreamingPrompt(process, prompt, responseStream);
        } finally {
            this.busyProcesses.delete(processId);
        }
    }

    async executeStreamingPrompt(process, prompt, responseStream) {
        return new Promise((resolve, reject) => {
            let isCollecting = false;
            let lastSentLength = 0;
            
            const timeout = setTimeout(() => {
                responseStream.write('data: {"error": "timeout"}\n\n');
                responseStream.end();
                reject(new Error('Streaming timeout'));
            }, 300000);

            const dataHandler = (data) => {
                const output = data.toString();
                
                if (!isCollecting && output.includes('\n')) {
                    isCollecting = true;
                }
                
                if (isCollecting) {
                    // 새로운 내용만 스트리밍
                    const newContent = output.substring(lastSentLength);
                    if (newContent && !newContent.includes('>>>')) {
                        responseStream.write(`data: ${JSON.stringify({response: newContent})}\n\n`);
                        lastSentLength += newContent.length;
                    }
                    
                    // 응답 완료 체크
                    if (output.includes('>>>')) {
                        clearTimeout(timeout);
                        process.stdout.removeListener('data', dataHandler);
                        responseStream.write('data: {"done": true}\n\n');
                        responseStream.end();
                        resolve();
                    }
                }
            };

            process.stdout.on('data', dataHandler);
            process.stdin.write(prompt + '\n');
        });
    }

    async getModels() {
        try {
            const { stdout } = await execAsync('ollama list');
            const lines = stdout.split('\n').slice(1); // 헤더 제외
            const models = lines
                .filter(line => line.trim())
                .map(line => {
                    const parts = line.split(/\s+/);
                    return {
                        name: parts[0],
                        size: parts[1] || 'unknown',
                        modified: parts.slice(2).join(' ') || 'unknown'
                    };
                });
            return models;
        } catch (error) {
            throw new Error(`Failed to get models: ${error.message}`);
        }
    }

    async generateEmbeddings(modelName, input) {
        try {
            const { stdout } = await execAsync(`ollama embed ${modelName} "${input}"`);
            return JSON.parse(stdout);
        } catch (error) {
            throw new Error(`Failed to generate embeddings: ${error.message}`);
        }
    }

    async cleanupOldProcesses() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30분
        
        for (const [key, processInfo] of this.processes.entries()) {
            if (now - processInfo.lastUsed > maxAge && !this.busyProcesses.has(processInfo.processId)) {
                try {
                    processInfo.process.kill();
                    this.processes.delete(key);
                    console.log(`Cleaned up old process: ${processInfo.processId}`);
                } catch (error) {
                    console.error(`Error cleaning up process ${processInfo.processId}:`, error);
                }
            }
        }
    }

    async cleanup() {
        clearInterval(this.cleanupInterval);
        
        for (const processInfo of this.processes.values()) {
            try {
                processInfo.process.kill();
            } catch (error) {
                console.error(`Error cleaning up process ${processInfo.processId}:`, error);
            }
        }
    }
}

// 전역 매니저 인스턴스
const ollamaManager = new OllamaManager();

// API 엔드포인트들
app.post('/api/generate', async (req, res) => {
    try {
        const { prompt, model = 'llama2', stream = false } = req.body;
        
        if (!prompt) {
            return res.status(400).json({ error: 'Prompt is required' });
        }

        if (stream) {
            res.writeHead(200, {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            });
            
            await ollamaManager.sendStreamingPrompt(model, prompt, res);
        } else {
            const response = await ollamaManager.sendPrompt(model, prompt);
            res.json({
                response,
                model,
                done: true
            });
        }
    } catch (error) {
        console.error('Generate error:', error);
        if (!res.headersSent) {
            res.status(500).json({ error: error.message });
        }
    }
});

app.get('/api/tags', async (req, res) => {
    try {
        const models = await ollamaManager.getModels();
        res.json({ models });
    } catch (error) {
        console.error('Tags error:', error);
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/embeddings', async (req, res) => {
    try {
        const { prompt, model = 'llama2' } = req.body;
        
        if (!prompt) {
            return res.status(400).json({ error: 'Prompt is required' });
        }

        const embeddings = await ollamaManager.generateEmbeddings(model, prompt);
        res.json({
            embedding: embeddings,
            model
        });
    } catch (error) {
        console.error('Embeddings error:', error);
        res.status(500).json({ error: error.message });
    }
});

app.get('/health', async (req, res) => {
    try {
        await execAsync('ollama list', { timeout: 5000 });
        res.json({ 
            status: 'healthy',
            timestamp: new Date().toISOString(),
            processes: Array.from(ollamaManager.processes.keys())
        });
    } catch (error) {
        res.status(503).json({ 
            status: 'unhealthy', 
            error: error.message 
        });
    }
});

// 에러 핸들링
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// 서버 시작
const PORT = process.env.PORT || 8000;

const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`Ollama API Bridge running on port ${PORT}`);
    console.log(`Available endpoints:`);
    console.log(`  POST /api/generate - Generate text`);
    console.log(`  GET  /api/tags - List available models`);
    console.log(`  POST /api/embeddings - Generate embeddings`);
    console.log(`  GET  /health - Health check`);
});

// 우아한 종료
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    await ollamaManager.cleanup();
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});

process.on('SIGTERM', async () => {
    console.log('SIGTERM received, shutting down...');
    await ollamaManager.cleanup();
    server.close(() => {
        process.exit(0);
    });
});
