{"name": "ollama-api-bridge", "version": "1.0.0", "description": "REST API bridge for Ollama with Node.js terminal control", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ollama-api", "pm2:restart": "pm2 restart ollama-api", "pm2:logs": "pm2 logs ollama-api"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "type": "module"}