// ollama-tmux-bridge.js
const express = require('express');
const { spawn, exec } = require('child_process');
const { promisify } = require('util');
const crypto = require('crypto');
const fs = require('fs').promises;

const execAsync = promisify(exec);
const app = express();
app.use(express.json());

class TmuxOllamaManager {
    constructor() {
        this.sessions = new Map();
        this.busySessions = new Set();
        this.cleanupInterval = setInterval(() => this.cleanupOldSessions(), 10 * 60 * 1000); // 10분마다
    }

    async getOrCreateSession(modelName) {
        const sessionKey = `ollama_${modelName}`;
        
        if (!this.sessions.has(sessionKey)) {
            const sessionId = `${sessionKey}_${crypto.randomBytes(4).toString('hex')}`;
            await this.createSession(sessionId, modelName);
            
            this.sessions.set(sessionKey, {
                sessionId,
                modelName,
                lastUsed: Date.now(),
                created: Date.now()
            });
        }

        this.sessions.get(sessionKey).lastUsed = Date.now();
        return this.sessions.get(sessionKey).sessionId;
    }

    async createSession(sessionId, modelName) {
        try {
            // 기존 세션 제거
            await execAsync(`tmux kill-session -t ${sessionId} 2>/dev/null || true`);
            
            // 새 세션 생성
            await execAsync(`tmux new-session -d -s ${sessionId}`);
            
            // ollama 실행
            await execAsync(`tmux send-keys -t ${sessionId} "ollama run ${modelName}" Enter`);
            
            // 모델 로딩 대기
            await this.waitForPrompt(sessionId, 30000);
            
            console.log(`Created tmux session: ${sessionId} for model: ${modelName}`);
        } catch (error) {
            console.error(`Failed to create session ${sessionId}:`, error);
            throw error;
        }
    }

    async waitForPrompt(sessionId, timeout = 30000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                const { stdout } = await execAsync(`tmux capture-pane -t ${sessionId} -p`);
                if (stdout.includes('>>>')) {
                    return true;
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`Error waiting for prompt in ${sessionId}:`, error);
            }
        }
        
        throw new Error(`Timeout waiting for prompt in session ${sessionId}`);
    }

    async sendPrompt(modelName, prompt) {
        const sessionId = await this.getOrCreateSession(modelName);
        
        // 세션 사용 중 체크
        if (this.busySessions.has(sessionId)) {
            throw new Error('Session is busy');
        }

        this.busySessions.add(sessionId);

        try {
            // 이전 내용 클리어
            await execAsync(`tmux clear-history -t ${sessionId}`);
            
            // 프롬프트 전송 (특수문자 이스케이프)
            const escapedPrompt = prompt
                .replace(/\\/g, '\\\\')
                .replace(/"/g, '\\"')
                .replace(/\n/g, '\\n');
            
            await execAsync(`tmux send-keys -t ${sessionId} "${escapedPrompt}" Enter`);
            
            // 응답 수집
            const response = await this.collectResponse(sessionId);
            return response;
            
        } finally {
            this.busySessions.delete(sessionId);
        }
    }

    async collectResponse(sessionId, timeout = 300000) { // 5분 타임아웃
        return new Promise((resolve, reject) => {
            let responseLines = [];
            let lastContent = '';
            let checkInterval;
            let timeoutHandle;

            const cleanup = () => {
                if (checkInterval) clearInterval(checkInterval);
                if (timeoutHandle) clearTimeout(timeoutHandle);
            };

            timeoutHandle = setTimeout(() => {
                cleanup();
                reject(new Error('Response timeout'));
            }, timeout);

            checkInterval = setInterval(async () => {
                try {
                    const { stdout } = await execAsync(`tmux capture-pane -t ${sessionId} -p`);
                    
                    if (stdout !== lastContent) {
                        const lines = stdout.trim().split('\n');
                        
                        // 마지막 줄이 프롬프트인지 확인 (응답 완료)
                        const lastLine = lines[lines.length - 1];
                        if (lastLine && lastLine.trim().startsWith('>>>')) {
                            // 응답 추출
                            const response = this.extractResponse(lines);
                            cleanup();
                            resolve(response);
                            return;
                        }
                        lastContent = stdout;
                    }
                } catch (error) {
                    cleanup();
                    reject(error);
                }
            }, 500);
        });
    }

    extractResponse(lines) {
        const responseLines = [];
        let foundStart = false;
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            
            // 입력 프롬프트나 시스템 메시지 스킵
            if (trimmedLine.startsWith('>>>') || trimmedLine.startsWith('"')) {
                foundStart = true;
                continue;
            }
            
            // 응답 종료 프롬프트 발견시 중단
            if (foundStart && trimmedLine.startsWith('>>>')) {
                break;
            }
            
            // 유효한 응답 라인만 수집
            if (foundStart && trimmedLine) {
                responseLines.push(line);
            }
        }
        
        return responseLines.join('\n').trim();
    }

    async sendStreamingPrompt(modelName, prompt, responseStream) {
        const sessionId = await this.getOrCreateSession(modelName);
        
        if (this.busySessions.has(sessionId)) {
            throw new Error('Session is busy');
        }

        this.busySessions.add(sessionId);

        try {
            await execAsync(`tmux clear-history -t ${sessionId}`);
            
            const escapedPrompt = prompt
                .replace(/\\/g, '\\\\')
                .replace(/"/g, '\\"')
                .replace(/\n/g, '\\n');
            
            await execAsync(`tmux send-keys -t ${sessionId} "${escapedPrompt}" Enter`);
            
            // 스트리밍 응답 수집
            await this.collectStreamingResponse(sessionId, responseStream);
            
        } finally {
            this.busySessions.delete(sessionId);
        }
    }

    async collectStreamingResponse(sessionId, responseStream) {
        return new Promise((resolve, reject) => {
            let lastContent = '';
            let checkInterval;
            let timeoutHandle;

            const cleanup = () => {
                if (checkInterval) clearInterval(checkInterval);
                if (timeoutHandle) clearTimeout(timeoutHandle);
            };

            timeoutHandle = setTimeout(() => {
                cleanup();
                responseStream.write('data: {"error": "timeout"}\n\n');
                responseStream.end();
                reject(new Error('Streaming timeout'));
            }, 300000);

            checkInterval = setInterval(async () => {
                try {
                    const { stdout } = await execAsync(`tmux capture-pane -t ${sessionId} -p`);
                    
                    if (stdout !== lastContent) {
                        const lines = stdout.trim().split('\n');
                        const lastLine = lines[lines.length - 1];
                        
                        // 새로운 내용 스트리밍
                        const newContent = this.extractNewContent(lastContent, stdout);
                        if (newContent) {
                            responseStream.write(`data: ${JSON.stringify({response: newContent})}\n\n`);
                        }
                        
                        // 응답 완료 체크
                        if (lastLine && lastLine.trim().startsWith('>>>')) {
                            responseStream.write('data: {"done": true}\n\n');
                            responseStream.end();
                            cleanup();
                            resolve();
                            return;
                        }
                        
                        lastContent = stdout;
                    }
                } catch (error) {
                    cleanup();
                    responseStream.write(`data: {"error": "${error.message}"}\n\n`);
                    responseStream.end();
                    reject(error);
                }
            }, 200);
        });
    }

    extractNewContent(oldContent, newContent) {
        // 새로운 내용만 추출하는 로직
        const oldLines = oldContent.split('\n');
        const newLines = newContent.split('\n');
        
        if (newLines.length > oldLines.length) {
            return newLines.slice(oldLines.length).join('\n');
        }
        
        return '';
    }

    async cleanupOldSessions() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30분
        
        for (const [key, session] of this.sessions.entries()) {
            if (now - session.lastUsed > maxAge && !this.busySessions.has(session.sessionId)) {
                try {
                    await execAsync(`tmux kill-session -t ${session.sessionId}`);
                    this.sessions.delete(key);
                    console.log(`Cleaned up old session: ${session.sessionId}`);
                } catch (error) {
                    console.error(`Error cleaning up session ${session.sessionId}:`, error);
                }
            }
        }
    }

    async cleanup() {
        clearInterval(this.cleanupInterval);
        
        for (const session of this.sessions.values()) {
            try {
                await execAsync(`tmux kill-session -t ${session.sessionId}`);
            } catch (error) {
                console.error(`Error cleaning up session ${session.sessionId}:`, error);
            }
        }
    }
}

// 전역 매니저 인스턴스
const ollamaManager = new TmuxOllamaManager();

// API 엔드포인트들
app.post('/api/generate', async (req, res) => {
    try {
        const { prompt, model = 'llama2', stream = false } = req.body;
        
        if (!prompt) {
            return res.status(400).json({ error: 'Prompt is required' });
        }

        if (stream) {
            res.writeHead(200, {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            });
            
            await ollamaManager.sendStreamingPrompt(model, prompt, res);
        } else {
            const response = await ollamaManager.sendPrompt(model, prompt);
            res.json({
                response,
                model,
                done: true
            });
        }
    } catch (error) {
        console.error('Generate error:', error);
        if (!res.headersSent) {
            res.status(500).json({ error: error.message });
        }
    }
});

app.get('/api/models', async (req, res) => {
    try {
        const { stdout } = await execAsync('ollama list');
        const lines = stdout.split('\n').slice(1); // 헤더 제외
        const models = lines
            .filter(line => line.trim())
            .map(line => {
                const parts = line.split(/\s+/);
                return {
                    name: parts[0],
                    size: parts[1] || 'unknown',
                    modified: parts.slice(2).join(' ') || 'unknown'
                };
            });
        
        res.json({ models });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.get('/health', async (req, res) => {
    try {
        await execAsync('ollama list', { timeout: 5000 });
        res.json({ 
            status: 'healthy',
            timestamp: new Date().toISOString(),
            sessions: Array.from(ollamaManager.sessions.keys())
        });
    } catch (error) {
        res.status(503).json({ 
            status: 'unhealthy', 
            error: error.message 
        });
    }
});

// 에러 핸들링
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// 서버 시작
const PORT = process.env.PORT || 8000;

const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`Ollama Tmux Bridge API running on port ${PORT}`);
});

// 우아한 종료
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    await ollamaManager.cleanup();
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});

process.on('SIGTERM', async () => {
    console.log('SIGTERM received, shutting down...');
    await ollamaManager.cleanup();
    server.close(() => {
        process.exit(0);
    });
});