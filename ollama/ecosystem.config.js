module.exports = {
  apps: [{
    name: 'ollama-api',
    script: 'app.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 8000
    },
    env_development: {
      NODE_ENV: 'development',
      PORT: 8000
    },
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    // PM2 클러스터 모드 비활성화 (ollama 프로세스 관리 때문에)
    exec_mode: 'fork',
    // 재시작 정책
    min_uptime: '10s',
    max_restarts: 10,
    // 메모리 모니터링
    kill_timeout: 5000,
    listen_timeout: 3000,
    // 환경 변수
    merge_logs: true,
    // 로그 로테이션
    log_type: 'json'
  }]
};
