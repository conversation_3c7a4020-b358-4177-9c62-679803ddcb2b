# Ollama API Bridge

Node.js 기반의 Ollama REST API 브리지입니다. tmux 대신 Node.js에서 직접 터미널을 제어하여 ollama CLI와 통신합니다.

## 특징

- **Node.js 터미널 제어**: tmux 대신 child_process를 사용하여 ollama CLI 직접 제어
- **PM2 지원**: 프로덕션 환경에서 PM2로 프로세스 관리
- **REST API**: 표준 Ollama API 엔드포인트 제공
- **스트리밍 지원**: 실시간 응답 스트리밍
- **자동 프로세스 관리**: 모델별 프로세스 생성 및 정리

## API 엔드포인트

### POST /api/generate
텍스트 생성 (gemma3:1b 모델만 사용)

```bash
curl -X POST http://localhost:8000/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemma3:1b",
    "prompt": "Hello, how are you?",
    "stream": false
  }'
```

### GET /api/tags
사용 가능한 모델 목록

```bash
curl http://localhost:8000/api/tags
```

### POST /api/embeddings
임베딩 생성 (nomic-embed-text 모델 사용)

```bash
curl -X POST http://localhost:8000/api/embeddings \
  -H "Content-Type: application/json" \
  -d '{
    "model": "nomic-embed-text:latest",
    "prompt": "Hello world"
  }'
```

### GET /health
헬스 체크

```bash
curl http://localhost:8000/health
```

## 설치 및 실행

### 1. 의존성 설치
```bash
npm install
```

### 2. 개발 모드 실행
```bash
npm run dev
```

### 3. PM2로 프로덕션 실행
```bash
# PM2 설치 (전역)
npm install -g pm2

# 애플리케이션 시작
npm run pm2:start

# 상태 확인
pm2 status

# 로그 확인
npm run pm2:logs

# 재시작
npm run pm2:restart

# 중지
npm run pm2:stop
```

## PM2 명령어

```bash
# 시작
pm2 start ecosystem.config.js

# 상태 확인
pm2 status
pm2 monit

# 로그 확인
pm2 logs ollama-api
pm2 logs ollama-api --lines 100

# 재시작
pm2 restart ollama-api

# 중지
pm2 stop ollama-api

# 삭제
pm2 delete ollama-api

# 모든 프로세스 중지
pm2 stop all

# PM2 데몬 재시작
pm2 kill
```

## 환경 변수

- `PORT`: 서버 포트 (기본값: 8000)
- `NODE_ENV`: 환경 모드 (development/production)

## 로그

로그는 `logs/` 디렉토리에 저장됩니다:
- `logs/out.log`: 표준 출력
- `logs/err.log`: 에러 로그
- `logs/combined.log`: 통합 로그

## 주의사항

1. **Ollama 설치 필요**: 시스템에 ollama CLI가 설치되어 있어야 합니다.
2. **모델 다운로드**: 사용할 모델이 미리 다운로드되어 있어야 합니다.
3. **메모리 관리**: 각 모델별로 별도 프로세스가 생성되므로 메모리 사용량을 모니터링하세요.
4. **프로세스 정리**: 30분 이상 사용되지 않은 프로세스는 자동으로 정리됩니다.

## 트러블슈팅

### 모델 로딩 실패
```bash
# 모델 목록 확인
ollama list

# 모델 다운로드
ollama pull llama2
```

### 프로세스 확인
```bash
# PM2 프로세스 상태
pm2 status

# 시스템 프로세스 확인
ps aux | grep ollama
```

### 로그 확인
```bash
# PM2 로그
pm2 logs ollama-api

# 애플리케이션 로그
tail -f logs/combined.log
```
